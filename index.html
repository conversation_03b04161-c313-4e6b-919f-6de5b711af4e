<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股债利差监控面板</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
        }

        .main-container {
            width: 100%;
            max-width: 1400px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #212529;
        }

        .update-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        #update-btn {
            padding: 8px 16px;
            font-size: 14px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        #update-btn:hover:not(:disabled) {
            background-color: #0056b3;
        }

        #update-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        #update-status {
            font-size: 14px;
            color: #6c757d;
        }

        /* Tab 样式 */
        .tab-container {
            display: flex;
            gap: 10px;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
        }

        .tab-btn {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border: none;
            background-color: transparent;
            border-bottom: 3px solid transparent;
            transition: color 0.3s, border-color 0.3s;
        }

        .tab-btn:hover {
            color: #0056b3;
        }

        .tab-btn.active {
            color: #007bff;
            border-bottom-color: #007bff;
            font-weight: 600;
        }

        /* 内容容器 */
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            width: 100%;
        }

        .info-card {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }

        .info-card h2 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 18px;
            color: #495057;
            border-bottom: 1px solid #f1f3f5;
            padding-bottom: 10px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            padding: 8px 0;
        }

        .info-item .label {
            color: #6c757d;
        }

        .info-item .value {
            font-weight: 600;
            color: #212529;
        }

        /* 公式和说明样式 */
        .formula-container {
            font-size: 1.1em;
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .explanation {
            font-size: 14px;
            color: #495057;
            line-height: 1.6;
        }

        .chart-container {
            width: 100%;
            height: 500px;
            background-color: #ffffff;
            border-radius: 12px;
            padding: 20px;
            box-sizing: border-box;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }

        /* 加载动画 */
        .loader-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            flex-direction: column;
            gap: 20px;
        }

        .spinner {
            border: 6px solid #f3f3f3;
            border-top: 6px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>

    <div id="loader" class="loader-overlay">
        <div class="spinner"></div>
        <p>正在加载初始数据，请稍候...</p>
    </div>

    <div class="main-container">
        <div class="header">
            <h1>股债利差监控面板</h1>
            <div class="update-container">
                <span id="update-status"></span>
                <button id="update-btn">更新数据</button>
            </div>
        </div>

        <div class="tab-container">
            <button class="tab-btn active" data-index="csi300">沪深300</button>
            <button class="tab-btn" data-index="sse50">上证50</button>
        </div>

        <div class="content-grid">
            <div class="info-card">
                <h2>核心指标 (<span id="latest-date">--</span>)</h2>
                <div class="info-item">
                    <span class="label">当前股债利差 (%):</span>
                    <span class="value" id="current-yield-spread">--</span>
                </div>
                <div class="info-item">
                    <span class="label">当前历史分位数:</span>
                    <span class="value" id="current-percentile">--</span>
                </div>
                <hr style="border: none; border-top: 1px solid #f1f3f5; margin: 10px 0;">
                <div class="info-item">
                    <span class="label">三年内最大值 (%):</span>
                    <span class="value" id="3y-max">--</span>
                </div>
                <div class="info-item">
                    <span class="label">80% 分位值 (%):</span>
                    <span class="value" id="80-percentile">--</span>
                </div>
                <div class="info-item">
                    <span class="label">20% 分位值 (%):</span>
                    <span class="value" id="20-percentile">--</span>
                </div>
                <div class="info-item">
                    <span class="label">三年内最小值 (%):</span>
                    <span class="value" id="3y-min">--</span>
                </div>

                <!-- 新增：指数详情部分 -->
                <hr style="border: none; border-top: 1px solid #e9ecef; margin: 15px 0;">
                <h3 style="font-size: 16px; color: #495057; margin-top: 0; margin-bottom: 10px;">指数详情</h3>
                
                <div class="info-item">
                    <span class="label">当前指数:</span>
                    <span class="value" id="index-name">--</span>
                </div>
                <div class="info-item">
                    <span class="label">最新收盘价:</span>
                    <span class="value" id="index-latest-close">--</span>
                </div>
                <div class="info-item">
                    <span class="label">滚动市盈率 (TTM):</span>
                    <span class="value" id="index-pe-ttm">--</span>
                </div>
            </div>

            <div class="info-card">
                <h2>策略信号</h2>
                
                <!-- 新增：明确的操作建议 -->
                <div class="info-item" style="flex-direction: column; align-items: flex-start; gap: 5px; margin-bottom: 10px;">
                    <span class="label">当前操作建议:</span>
                    <span class="value" id="signal-recommendation" style="font-size: 22px; font-weight: bold;">--</span>
                </div>
            
                <hr style="border: none; border-top: 1px solid #f1f3f5; margin: 10px 0;">
            
                <!-- 新增：显示计算所用的分位数 -->
                <div class="info-item">
                    <span class="label">计算依据 (历史分位数):</span>
                    <span class="value" id="signal-basis-percentile">--</span>
                </div>
            
                <!-- 原有的精确信号值 -->
                <div class="info-item">
                    <span class="label">精确信号值:</span>
                    <span class="value" id="current-signal-value">--</span>
                </div>
            
                <h3>信号计算公式</h3>
                <div class="formula-container">
                    <img src="{{ url_for('static', filename='formula.png') }}" alt="公式图片" style="max-width: 100%; height: auto; display: block; margin: 0 auto;">
                </div>
                
                <h3>买卖逻辑说明</h3>
                <p class="explanation">
                    该策略基于股债利差的历史分位数生成交易信号。
                    <br>• 当股债利差的<strong>历史分位数较高</strong>时 (例如 > 88%)，意味着股票相对于债券极具吸引力，策略会生成<strong>买入信号</strong>。
                    <br>• 当股债利差的<strong>历史分位数较低</strong>时 (例如 < 24%)，意味着股票相对于债券估值偏高，策略会生成<strong>卖出信号</strong>。
                    <br>• 在中间区域则<strong>持有</strong>。
                    <br>信号值为正数代表买入比例，负数代表卖出比例。
                </p>
            </div>
        </div>

        <div id="main-chart" class="chart-container"></div>
        <div id="signal-chart" class="chart-container"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // --- 全局变量 ---
            const loader = document.getElementById('loader');
            const updateBtn = document.getElementById('update-btn');
            const updateStatus = document.getElementById('update-status');
            const mainChart = echarts.init(document.getElementById('main-chart'));
            const signalChart = echarts.init(document.getElementById('signal-chart'));
            let marketData = {};
            let activeIndexId = 'csi300';

            async function fetchDataAndRender() {
                loader.style.display = 'flex';
                try {
                    const response = await fetch('/api/data');
                    if (!response.ok) {
                        let errorMessage = `HTTP error! status: ${response.status}`;
                        try {
                            const errorData = await response.json();
                            if (errorData && errorData.error) errorMessage = errorData.error;
                        } catch (e) {
                            const text = await response.text();
                            errorMessage = `HTTP ${response.status}: ${text.substring(0, 200)}`;
                        }
                        throw new Error(errorMessage);
                    }
                    const raw = await response.text();
                    try {
                        marketData = JSON.parse(raw);
                    } catch (parseErr) {
                        const sanitized = raw.replace(/\bNaN\b/g, 'null').replace(/\bInfinity\b/g, 'null').replace(/-Infinity/g, 'null');
                        marketData = JSON.parse(sanitized);
                        console.warn('Response contained non-JSON numeric values; sanitized to null.');
                    }

                    switchTab(activeIndexId);

                } catch (error) {
                    updateStatus.textContent = `数据加载失败: ${error.message}`;
                    console.error('Fetch error:', error);
                } finally {
                    loader.style.display = 'none';
                }
            }

            /**
             * 切换标签页
             * @param {string} indexId - 'csi300' or 'sse50'
             */
            function switchTab(indexId) {
                activeIndexId = indexId;
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.index === indexId);
                });
                updateUI();
            }

            /**
             * 根据当前激活的指数ID，更新所有UI组件
             */
            function updateUI() {
                if (!marketData.dates || marketData.dates.length === 0) return;

                const indexData = {
                    dates: marketData.dates,
                    index_points: marketData[`${activeIndexId}_index`],
                    pe_history: marketData[`${activeIndexId}_pe`], // 新增PE数据
                    yield_spread: marketData[`${activeIndexId}_yield_spread`],
                    percentile_history: marketData[`${activeIndexId}_percentile`],
                    yield_spread_max: marketData[`${activeIndexId}_yield_spread_max`], // 三年最大值
                    yield_spread_min: marketData[`${activeIndexId}_yield_spread_min`], // 三年最小值
                    percentile_80_line: marketData[`${activeIndexId}_yield_spread_80pct`],
                    percentile_20_line: marketData[`${activeIndexId}_yield_spread_20pct`],
                    latest_percentile: marketData[`latest_${activeIndexId}_percentile`],
                    strategy_signal: marketData[`${activeIndexId}_strategy_signal`],
                    bond_yield_10y: marketData.bond_yield_10y,
                    historical_signals: marketData[`${activeIndexId}_historical_signals`],
                };

                updateStatCard(indexData);
                renderChart(indexData);
                renderSignalChart(indexData);
            }

            /**
             * 更新核心指标卡片
             */
            function updateStatCard(data) {
                const lastIndex = data.dates.length - 1;
                if (lastIndex < 0) return;

                const latestDate = data.dates[lastIndex];

                // --- 使用后端计算的三年统计数据 ---
                const stat3y = {
                    min: data.yield_spread_min[lastIndex],
                    max: data.yield_spread_max[lastIndex],
                    p20: data.percentile_20_line[lastIndex],
                    p80: data.percentile_80_line[lastIndex],
                };

                // --- 格式化函数 ---
                const formatValue = (val, digits = 2) => (val !== null && val !== undefined) ? val.toFixed(digits) : '--';
                const formatPercent = (val) => (val !== null && val !== undefined) ? `${(val * 100).toFixed(2)}%` : '--';
                const formatSignal = (val) => {
                    if (val === null || val === undefined) return '--';
                    if (val > 0) return `买入 ${(val * 100).toFixed(2)}%`;
                    if (val < 0) return `卖出 ${(Math.abs(val) * 100).toFixed(2)}%`;
                    return '持有 0.00%';
                };

                // --- DOM更新 ---
                // 核心指标卡片
                document.getElementById('latest-date').textContent = latestDate || '--';
                document.getElementById('current-yield-spread').textContent = formatValue(data.yield_spread[lastIndex]);
                document.getElementById('current-percentile').textContent = formatPercent(data.latest_percentile);
                document.getElementById('3y-max').textContent = formatValue(stat3y.max);
                document.getElementById('80-percentile').textContent = formatValue(stat3y.p80);
                document.getElementById('20-percentile').textContent = formatValue(stat3y.p20);
                document.getElementById('3y-min').textContent = formatValue(stat3y.min);
                
                // 指数详情更新
                const indexName = activeIndexId === 'csi300' ? '沪深300' : '上证50';
                document.getElementById('index-name').textContent = indexName;
                document.getElementById('index-latest-close').textContent = formatValue(data.index_points[lastIndex]);
                document.getElementById('index-pe-ttm').textContent = formatValue(data.pe_history[lastIndex]);

                // 策略信号卡片更新
                const signalValue = data.strategy_signal;
                const recommendationEl = document.getElementById('signal-recommendation');
                
                if (signalValue > 0) {
                    recommendationEl.textContent = '建议买入';
                    recommendationEl.style.color = '#28a745'; // 绿色
                } else if (signalValue < 0) {
                    recommendationEl.textContent = '建议卖出';
                    recommendationEl.style.color = '#dc3545'; // 红色
                } else {
                    recommendationEl.textContent = '建议持有';
                    recommendationEl.style.color = '#6c757d'; // 灰色
                }
                
                document.getElementById('signal-basis-percentile').textContent = formatPercent(data.latest_percentile);
                document.getElementById('current-signal-value').textContent = formatSignal(signalValue);
            }


            /**
             * 渲染或更新图表
             */
            function renderChart(data) {
                const indexName = activeIndexId === 'csi300' ? '沪深300' : '上证50';
                const option = {
                    title: { text: `${indexName}指数与股债利差`, left: 'center' },
                    tooltip: { trigger: 'axis', axisPointer: { type: 'cross' } },
                    legend: { top: 30, data: ['指数点位', '股债利差', '历史分位数', '80%分位线', '20%分位线'] },
                    grid: { left: '50px', right: '50px', bottom: '80px' },
                    xAxis: { type: 'category', data: data.dates },
                    yAxis: [
                        { type: 'value', name: '指数点位', position: 'left', alignTicks: true, axisLine: { show: true, lineStyle: { color: '#5470C6' } } },
                        { type: 'value', name: '利差 (%)', position: 'right', alignTicks: true, axisLine: { show: true, lineStyle: { color: '#91CC75' } }, axisLabel: { formatter: '{value}%' } },
                        { type: 'value', name: '分位数', position: 'right', offset: 60, min: 0, max: 1, axisLine: { show: true, lineStyle: { color: '#FC8452' } }, axisLabel: { formatter: val => `${(val * 100).toFixed(0)}%` } }
                    ],
                    dataZoom: [
                        { type: 'slider', start: 80, end: 100, bottom: 20 },
                        { type: 'inside', start: 80, end: 100 }
                    ],
                    series: [
                        { name: '指数点位', type: 'line', yAxisIndex: 0, data: data.index_points, showSymbol: false, lineStyle: { color: '#5470C6' } },
                        { name: '股债利差', type: 'line', yAxisIndex: 1, data: data.yield_spread, showSymbol: false, lineStyle: { color: '#91CC75', width: 2 } },
                        { name: '历史分位数', type: 'line', yAxisIndex: 2, data: data.percentile_history, showSymbol: false, lineStyle: { color: '#FC8452', width: 2 } },
                        { name: '80%分位线', type: 'line', yAxisIndex: 1, data: data.percentile_80_line, showSymbol: false, lineStyle: { type: 'dashed', color: '#FAC858' } },
                        { name: '20%分位线', type: 'line', yAxisIndex: 1, data: data.percentile_20_line, showSymbol: false, lineStyle: { type: 'dashed', color: '#EE6666' } }
                    ]
                };
                mainChart.setOption(option, true);
            }

            /**
             * 渲染历史买卖信号图表
             */
            function renderSignalChart(data) {
                const indexName = activeIndexId === 'csi300' ? '沪深300' : '上证50';
                const historicalSignals = data.historical_signals || [];
                const buySignals = [];
                const sellSignals = [];

                for (let i = 0; i < data.dates.length; i++) {
                    const signal = historicalSignals[i];
                    if (signal > 0) {
                        buySignals.push([data.dates[i], signal * 100]);
                    } else if (signal < 0) {
                        sellSignals.push([data.dates[i], Math.abs(signal) * 100]);
                    }
                }

                const option = {
                    title: { text: `${indexName}历史买卖信号`, left: 'center' },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            let result = params[0].axisValue + '<br/>';
                            params.forEach(param => {
                                if (param.seriesName === '买入信号') {
                                    result += `${param.seriesName}: +${param.value[1].toFixed(2)}%<br/>`;
                                } else if (param.seriesName === '卖出信号') {
                                    result += `${param.seriesName}: -${param.value[1].toFixed(2)}%<br/>`;
                                } else {
                                    result += `${param.seriesName}: ${param.value.toFixed(4)}<br/>`;
                                }
                            });
                            return result;
                        }
                    },
                    legend: { top: 30, data: ['历史分位数', '买入信号', '卖出信号'] },
                    grid: { left: '50px', right: '50px', bottom: '80px' },
                    xAxis: { type: 'category', data: data.dates },
                    yAxis: [
                        {
                            type: 'value',
                            name: '分位数',
                            position: 'left',
                            min: 0,
                            max: 1,
                            axisLabel: { formatter: val => `${(val * 100).toFixed(0)}%` }
                        },
                        {
                            type: 'value',
                            name: '信号强度 (%)',
                            position: 'right',
                            axisLabel: { formatter: '{value}%' }
                        }
                    ],
                    dataZoom: [
                        { type: 'slider', start: 80, end: 100, bottom: 20 },
                        { type: 'inside', start: 80, end: 100 }
                    ],
                    series: [
                        {
                            name: '历史分位数',
                            type: 'line',
                            yAxisIndex: 0,
                            data: data.percentile_history,
                            showSymbol: false,
                            lineStyle: { color: '#FC8452', width: 1 }
                        },
                        {
                            name: '买入信号',
                            type: 'scatter',
                            yAxisIndex: 1,
                            data: buySignals,
                            symbolSize: 8,
                            itemStyle: { color: '#28a745' }
                        },
                        {
                            name: '卖出信号',
                            type: 'scatter',
                            yAxisIndex: 1,
                            data: sellSignals,
                            symbolSize: 8,
                            itemStyle: { color: '#dc3545' }
                        }
                    ]
                };
                signalChart.setOption(option, true);
            }

            // --- 事件绑定 ---
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', () => switchTab(btn.dataset.index));
            });

            updateBtn.addEventListener('click', async () => {
                updateBtn.disabled = true;
                updateStatus.textContent = '正在更新数据，请耐心等待...';
                loader.style.display = 'flex';

                try {
                    const response = await fetch('/api/update', { method: 'POST' });
                    const result = await response.json();
                    if (result.status === 'success') {
                        updateStatus.textContent = '更新成功！正在刷新...';
                        await fetchDataAndRender();
                        updateStatus.textContent = '图表已刷新。';
                    } else {
                        throw new Error(result.message || '未知错误');
                    }
                } catch (error) {
                    updateStatus.textContent = `更新失败: ${error.message}`;
                    console.error('Update error:', error);
                } finally {
                    updateBtn.disabled = false;
                    loader.style.display = 'none';
                    setTimeout(() => { updateStatus.textContent = ''; }, 3000);
                }
            });

            window.addEventListener('resize', () => {
                mainChart.resize();
                signalChart.resize();
            });

            // --- 初始加载 ---
            fetchDataAndRender();
        });
    </script>
</body>

</html>
